/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      VPNService+Monitoring.swift
 *
 * DESCRIPTION :    Monitoring and statistics management extension for VPNService.
 *                  Contains traffic monitoring, performance data collection, and
 *                  heartbeat handling functionality. Migrated from VPNService.swift
 *                  to improve code organization and maintainability.
 *
 * ARCHITECTURE :   - Unified monitoring lifecycle management
 *                  - Optimized timer implementation using DispatchSourceTimer
 *                  - Integrated heartbeat and traffic statistics handling
 *                  - Consistent error handling and logging patterns
 *                  - Thread-safe delegate notifications
 *
 * AUTHOR :         wei
 *
 * HISTORY :        03/07/2025 create - Migrated and optimized from VPNService.swift
 ******************************************************************************/

import Foundation
import NetworkExtension
import OSLog

// MARK: - VPNService Monitoring Extension

extension VPNService {
    
    // MARK: - Monitoring State Properties
    
    /// Connection start timestamp for duration calculations
    internal var connectionStartTime: Date? {
        get {
            return _connectionStartTime
        }
        set {
            _connectionStartTime = newValue
        }
    }
    
    /// Last traffic update timestamp
    internal var lastTrafficUpdate: Date {
        get {
            return _lastTrafficUpdate
        }
        set {
            _lastTrafficUpdate = newValue
        }
    }
    
    // MARK: - Private Storage Properties
    
    private var _connectionStartTime: Date? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.connectionStartTime) as? Date
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.connectionStartTime, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    private var _lastTrafficUpdate: Date {
        get {
            return (objc_getAssociatedObject(self, &AssociatedKeys.lastTrafficUpdate) as? Date) ?? Date()
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.lastTrafficUpdate, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    private var trafficUpdateTimer: DispatchSourceTimer? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.trafficUpdateTimer) as? DispatchSourceTimer
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.trafficUpdateTimer, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }

    private var stateValidationTimer: DispatchSourceTimer? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.stateValidationTimer) as? DispatchSourceTimer
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.stateValidationTimer, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    


    // MARK: - Associated Object Keys

    private struct AssociatedKeys {
        static var connectionStartTime: UInt8 = 0
        static var lastTrafficUpdate: UInt8 = 0
        static var trafficUpdateTimer: UInt8 = 0
        static var stateValidationTimer: UInt8 = 0
    }
    
    // MARK: - Monitoring Lifecycle Management
    
    /**
     * NAME: startMonitoring
     *
     * DESCRIPTION:
     *     Starts heartbeat, traffic monitoring, and state validation.
     *     Traffic monitoring is essential for sending traffic events to Flutter.
     *     State validation helps detect and recover from stuck states.
     */
    internal func startMonitoring() async {
        guard configuration.monitoring.enabled else {
            // logger.debug("Monitoring disabled in configuration") // Debug log commented for production
            return
        }

        logger.info("Starting VPN monitoring (heartbeat + traffic + state validation)", metadata: [
            "heartbeat_interval": "\(configuration.heartbeatInterval)",
            "traffic_interval": "\(configuration.trafficUpdateInterval)"
        ])

        // Setup heartbeat monitoring
        await setupHeartbeatMonitoring()

        // Start traffic monitoring for periodic traffic events
        startTrafficMonitoring()

        // logger.debug("VPN monitoring started successfully (heartbeat + traffic)") // Debug log commented for production
    }
    
    /**
     * NAME: stopMonitoring
     *
     * DESCRIPTION:
     *     Stops heartbeat monitoring and cleans up resources.
     *     Traffic statistics are now handled by ConnectionManager directly.
     */
    internal func stopMonitoring() {
        // logger.debug("Stopping VPN heartbeat monitoring") // Debug log commented for production

        // Reset monitoring state
        connectionStartTime = nil

        // logger.debug("VPN heartbeat monitoring stopped successfully") // Debug log commented for production
    }


    
    // MARK: - Traffic Statistics Management
    



    
    // MARK: - Private Traffic Monitoring Implementation
    
    /**
     * NAME: startTrafficMonitoring
     *
     * DESCRIPTION:
     *     Starts background traffic monitoring with optimized DispatchSourceTimer.
     *     Replaces Timer.scheduledTimer for better performance and control.
     */
    private func startTrafficMonitoring() {
        guard configuration.monitoring.enabled else {
            return
        }
        
        // logger.debug("Starting traffic monitoring", metadata: [
        //     "update_interval": "\(configuration.trafficUpdateInterval)"
        // ]) // Debug log commented for production
        
        // Create dispatch timer for better performance
        let timer = DispatchSource.makeTimerSource(queue: DispatchQueue.global(qos: .utility))
        timer.schedule(deadline: .now() + configuration.trafficUpdateInterval, 
                      repeating: configuration.trafficUpdateInterval)
        
        timer.setEventHandler { [weak self] in
            Task {
                await self?.updateTrafficStatistics()
            }
        }
        
        trafficUpdateTimer = timer
        timer.resume()
    }
    
    /**
     * NAME: stopTrafficMonitoring
     *
     * DESCRIPTION:
     *     Stops background traffic monitoring and cleans up timer resources.
     */
    private func stopTrafficMonitoring() {
        trafficUpdateTimer?.cancel()
        trafficUpdateTimer = nil
        // logger.debug("Traffic monitoring stopped") // Debug log commented for production
    }
    
    /**
     * NAME: updateTrafficStatistics
     *
     * DESCRIPTION:
     *     Gets traffic statistics from ConnectionManager and notifies delegate.
     *     ConnectionManager now handles all traffic calculations.
     */
    private func updateTrafficStatistics() async {
        guard await lifecycleManager.isStarted() else {
            // logger.debug("Skipping update - service not started") // Debug log commented for production
            return
        }

        // Use unified state management
        guard currentState.isConnected else {
            // logger.debug("Skipping update - not connected, state: \(currentState)") // Debug log commented for production
            return
        }

        // Get traffic statistics from shared App Group data (for VPN Extension compatibility)
        let stats = getSharedTrafficStatisticsForMonitoring()

        // Always send traffic updates when connected (even if zeros) to maintain consistency
        // This matches Go backend behavior which sends traffic events periodically

        lastTrafficUpdate = Date()

        logger.info("Sending traffic update to delegate", metadata: [
            "total_upload": "\(stats.totalUpload)",
            "total_download": "\(stats.totalDownload)",
            "upload_speed": "\(stats.uploadSpeed)",
            "download_speed": "\(stats.downloadSpeed)"
        ])

        // Notify delegate using unified notification pattern
        notifyDelegate { delegate in
            delegate.vpnService(self, didUpdateTraffic: stats)
        }
    }
    
    // MARK: - Heartbeat Monitoring
    
    /**
     * NAME: setupHeartbeatMonitoring
     *
     * DESCRIPTION:
     *     Sets up heartbeat callback with ConnectionManager for latency monitoring.
     *     Integrated with monitoring lifecycle for unified management.
     */
    private func setupHeartbeatMonitoring() async {
        // logger.debug("Setting up heartbeat monitoring") // Debug log commented for production

        await connectionManager.registerHeartbeatCallback { [weak self] (latency: UInt32) in
            Task {
                guard let self = self else { return }
                await self.handleHeartbeatReceived(latency)
            }
        }

        // logger.debug("Heartbeat monitoring setup completed") // Debug log commented for production
    }

    // MARK: - Shared Statistics Helper

    /**
     * NAME: getSharedTrafficStatisticsForMonitoring
     *
     * DESCRIPTION:
     *     Gets traffic statistics from App Group shared data for monitoring.
     *     This allows main app to access VPN Extension's traffic statistics.
     *
     * RETURNS:
     *     TrafficStatistics - Current traffic statistics from VPN Extension
     */
    private func getSharedTrafficStatisticsForMonitoring() -> TrafficStatistics {
        guard let userDefaults = UserDefaults(suiteName: "group.com.panabit.PanabitClient"),
              let sharedStats = userDefaults.object(forKey: "vpn_traffic_statistics") as? [String: Any] else {
            // logger.debug("No shared traffic statistics available, returning empty stats") // Debug log commented for production
            return TrafficStatistics()
        }

        // Check if data is recent (within last 30 seconds)
        let timestamp = sharedStats["timestamp"] as? TimeInterval ?? 0
        let age = Date().timeIntervalSince1970 - timestamp

        if age > 30 {
            // logger.debug("Shared traffic statistics are stale", metadata: [
            //     "age_seconds": "\(age)"
            // ]) // Debug log commented for production
            return TrafficStatistics()
        }

        let stats = TrafficStatistics(
            totalUpload: sharedStats["totalUpload"] as? Int64 ?? 0,
            totalDownload: sharedStats["totalDownload"] as? Int64 ?? 0,
            uploadSpeed: sharedStats["uploadSpeed"] as? Int64 ?? 0,
            downloadSpeed: sharedStats["downloadSpeed"] as? Int64 ?? 0,
            lastUpdate: Date(timeIntervalSince1970: sharedStats["lastUpdate"] as? TimeInterval ?? 0)
        )

        // logger.debug("Retrieved shared traffic statistics", metadata: [
        //     "total_upload": "\(stats.totalUpload)",
        //     "total_download": "\(stats.totalDownload)",
        //     "upload_speed": "\(stats.uploadSpeed)",
        //     "download_speed": "\(stats.downloadSpeed)",
        //     "age_seconds": "\(age)"
        // ]) // Debug log commented for production

        return stats
    }
    
    /**
     * NAME: handleHeartbeatReceived
     *
     * DESCRIPTION:
     *     Handles received heartbeat with latency information.
     *     Notifies delegate, logs heartbeat data, and updates auto-reconnection manager.
     *
     * PARAMETERS:
     *     latency - Heartbeat latency in milliseconds
     */
    internal func handleHeartbeatReceived(_ latency: UInt32) async {
        // logger.debug("Heartbeat received", metadata: [
        //     "latency_ms": "\(latency)",
        //     "connection_state": currentState.description
        // ]) // Debug log commented for production

        // Heartbeat monitoring is now handled by ConnectionManager
        // No need to update auto-reconnection manager as it's been removed

        // Notify delegate using unified notification pattern
        notifyDelegate { delegate in
            delegate.vpnService(self, didReceiveHeartbeat: latency)
        }
    }
    
    // MARK: - Error Handling and Utilities
    
    /**
     * NAME: handleMonitoringError
     *
     * DESCRIPTION:
     *     Unified error handling for monitoring operations with consistent logging.
     *
     * PARAMETERS:
     *     error - Error that occurred
     *     operation - Name of the operation that failed
     */
    private func handleMonitoringError(_ error: Error, operation: String) {
        logger.warning("Monitoring operation failed", metadata: [
            "operation": operation,
            "error": error.localizedDescription,
            "connection_state": currentState.description
        ])
    }
    
    // Note: notifyDelegate method is defined in main VPNService.swift file
}
